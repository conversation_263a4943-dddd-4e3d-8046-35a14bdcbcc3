<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Baseline Alignment Test</title>
    <link rel="stylesheet" href="components/charts/snap-charts.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .chart-container {
            height: 300px;
            margin: 20px 0;
            border: 1px solid #ddd; /* Add border to see exact boundaries */
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .test-info h4 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        .test-info ul {
            margin: 0;
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <h1>Baseline Alignment & Proportional Height Test</h1>
    
    <div class="test-info">
        <h4>Testing Criteria:</h4>
        <ul>
            <li><strong>Proportional Heights:</strong> US segments should be visibly taller than UK segments in both charts</li>
            <li><strong>Baseline Alignment:</strong> Bottom of all columns should touch the bottom grid line (no floating)</li>
            <li><strong>Scrollbar Position:</strong> Scrollbar should appear at the bottom of scrollable charts</li>
            <li><strong>Tooltip Colors:</strong> Main columns show segment color circles, comparison columns show marketplace data WITHOUT color circles</li>
            <li><strong>Consistency:</strong> Both chart types should behave identically</li>
        </ul>
    </div>
    
    <div class="comparison">
        <div class="test-container">
            <h3>Stacked Column with Compare</h3>
            <div id="chart1" class="chart-container"></div>
        </div>

        <div class="test-container">
            <h3>Stacked Column Scrollable</h3>
            <div id="chart2" class="chart-container"></div>
        </div>
    </div>

    <div class="test-container">
        <h3>Compare Mode Test (for tooltip segment colors)</h3>
        <div id="chart3" class="chart-container"></div>
    </div>
    </div>

    <script src="components/charts/snap-charts.js"></script>
    <script>
        // Test data with clear proportional differences and multiple data points
        const testData = [
            {
                month: 'JAN',
                day: '15',
                year: '24',
                sales: 100,
                royalties: 25,
                values: [68, 32], // US: 68, UK: 32 - clear 2:1 ratio
                labels: ['US', 'UK']
            },
            {
                month: 'FEB',
                day: '15',
                year: '24',
                sales: 150,
                royalties: 35,
                values: [90, 60], // US: 90, UK: 60 - 3:2 ratio
                labels: ['US', 'UK']
            },
            {
                month: 'MAR',
                day: '15',
                year: '24',
                sales: 80,
                royalties: 20,
                values: [50, 30], // US: 50, UK: 30 - 5:3 ratio
                labels: ['US', 'UK']
            },
            {
                month: 'APR',
                day: '15',
                year: '24',
                sales: 120,
                royalties: 30,
                values: [80, 40], // US: 80, UK: 40 - 2:1 ratio
                labels: ['US', 'UK']
            }
        ];

        // Comparison data for testing tooltips
        const comparisonData = [
            { month: 'JAN', day: '15', year: '23', sales: 80, royalties: 20, values: [80], labels: ['Previous'] },
            { month: 'FEB', day: '15', year: '23', sales: 120, royalties: 30, values: [120], labels: ['Previous'] },
            { month: 'MAR', day: '15', year: '23', sales: 60, royalties: 15, values: [60], labels: ['Previous'] },
            { month: 'APR', day: '15', year: '23', sales: 100, royalties: 25, values: [100], labels: ['Previous'] }
        ];

        // Initialize both chart types with identical data
        const chart1 = new SnapChart({
            container: '#chart1',
            type: 'stacked-column',
            data: testData,
            options: {
                responsive: true,
                animate: false // Disable animation for easier comparison
            }
        });

        const chart2 = new SnapChart({
            container: '#chart2',
            type: 'scrollable-stacked-column',
            data: testData,
            options: {
                responsive: true,
                animate: false // Disable animation for easier comparison
            }
        });

        // Initialize compare mode chart to test tooltip segment colors
        const chart3 = new SnapChart({
            container: '#chart3',
            type: 'stacked-column',
            data: testData,
            options: {
                responsive: true,
                animate: false,
                compareMode: true,
                compareData: comparisonData
            }
        });

        console.log('Charts initialized for baseline alignment test');
        
        // Add visual inspection helper
        setTimeout(() => {
            console.log('Visual inspection points:');
            console.log('1. Check if US segments are proportionally taller than UK segments');
            console.log('2. Check if column bottoms touch the bottom grid line');
            console.log('3. Compare alignment between both chart types');
        }, 1000);
    </script>
</body>
</html>
