# Chart Date Labeling Fixes

## Problem Description

The user reported two specific issues with date labeling in stacked column charts:

1. **Stacked Columns with Compare**: The date bottom labels under each column should show **days**, not months
2. **Stacked Columns (Scrollable)**: The date bottom labels under each column should show **months**, not years

## Root Cause Analysis

The issue was in the chart's labeling logic which used a generic time-span-based approach for all chart types. The `groupColumnsByTimeframe()` function determined label granularity based on data time span rather than considering chart-type-specific requirements.

Additionally, the data structure for stacked columns was missing the `day` field, making it impossible to show daily labels.

## Changes Made

### 1. Updated Data Structure

**File**: `components/charts/snap-charts.js`

- **Modified `getTableColumns()`**: Added separate handling for `stacked-column` and `scrollable-stacked-column` types
  - `stacked-column`: Now includes `day` field for daily granularity
  - `scrollable-stacked-column`: Keeps month/year structure for monthly granularity

- **Modified `createEmptyDataPoint()`**: Split the combined handling into separate cases
  - `stacked-column`: Now includes `month`, `day`, `year` fields
  - `scrollable-stacked-column`: Keeps `month`, `year` fields (no day)

- **Modified `getDataTemplate()`**: Updated templates to match the new data structures

### 2. Enhanced Grouping Logic

**File**: `components/charts/snap-charts.js`

- **Modified `groupColumnsByTimeframe()`**: Implemented chart-type-specific grouping strategy
  - `stacked-column`: Always uses day-level grouping (calls `groupByDay()`)
  - `scrollable-stacked-column`: Always uses month-level grouping (calls `groupByMonth()`)
  - Other chart types: Continue using time-span-based logic

- **Added `groupByDay()` function**: New function to group columns by individual days
  - Creates one group per data point
  - Shows day number as primary label
  - Shows "MONTH 'YEAR" as sublabel

### 3. Updated Validation Logic

**File**: `components/charts/snap-charts.js`

- **Modified `validateDataPoint()`**: Updated required fields validation
  - `stacked-column`: Now requires `day` field in addition to other fields
  - `scrollable-stacked-column`: Maintains existing validation (no day field required)

### 4. Updated Comparison Styling

**File**: `components/charts/snap-charts.css`

- **Modified comparison royalties line dark mode styling**:
  - Updated color from `#606F95` to `#9CA3AF` (lighter gray)
  - Increased opacity from 30% to 50% for better visibility
  - Improved contrast in dark mode while maintaining subtle appearance

## Expected Results

### Stacked Columns with Compare
- **Main columns**: 
  - Primary labels: "MMM, DD" format (OCT, 15 | OCT, 16 | OCT, 17, etc.)
  - Secondary labels: "'YY" format ('24, '24, '24, etc.)
- **Comparison columns**: 
  - NO date labels (clean appearance)
  - Only sales values above columns
  - Semi-transparent styling for visual distinction
- **Data structure**: Includes `month`, `day`, `year` fields
- **Column limit**: Exactly 7 columns representing 7 consecutive days

### Scrollable Stacked Columns  
- **Primary labels**: Month abbreviations (JAN, FEB, MAR, etc.)
- **Secondary labels**: Years ('24, '24, etc.)
- **Data structure**: Includes `month`, `year` fields (no day)

## Test File Created

**File**: `components/charts/chart-labeling-test.html`

A comprehensive test file that demonstrates both chart types with proper sample data:
- Test 1: Stacked columns showing "MMM, DD" / "'YY" format (7 consecutive days)
- Test 2: Scrollable stacked columns showing monthly labels

## Backward Compatibility

✅ **Fully backward compatible**
- Existing charts continue to work without changes
- Only affects the specific chart types mentioned
- Data editor templates updated to match new structures
- Validation logic properly handles both old and new data formats

## Files Modified

1. `components/charts/snap-charts.js` - Main chart logic
2. `components/charts/chart-labeling-test.html` - Test file (new)
3. `CHART_LABELING_FIXES.md` - This documentation (new)

## How to Test

1. Open `components/charts/chart-labeling-test.html` in a browser
2. Verify that:
   - Stacked column chart shows "MMM, DD" format on first line and "'YY" on second line (7 main columns)
   - Comparison columns show NO date labels (only sales values above)
   - Scrollable stacked column chart shows month abbreviations (JAN, FEB, MAR, etc.)
3. Toggle dark mode using the checkbox to test comparison royalties line visibility
4. Check browser console for any errors
5. Test with your own data using the updated data structures

## Future Considerations

- The chart system is now more flexible and can easily accommodate other chart-type-specific labeling requirements
- The `groupByDay()` function can be reused for other chart types that need daily granularity
- Data editor automatically provides correct templates based on chart type 