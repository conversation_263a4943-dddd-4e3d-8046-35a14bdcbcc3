<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Cards Min-Width Test</title>
    <link rel="stylesheet" href="snapapp.css">
    <style>
        body {
            font-family: 'Amazon Ember', -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            color: #333;
        }
        
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 8px;
            color: #1a1a1a;
        }
        
        .test-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
        }
        
        .resize-instructions {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 20px;
            font-size: 13px;
            color: #92400e;
        }
        
        .width-display {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #1f2937;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            z-index: 1000;
        }
        
        /* Simulate main content container */
        .main-content {
            width: 100%;
            padding: 20px;
            box-sizing: border-box;
        }
        
        /* Test cards with different types */
        .test-card {
            margin-bottom: 20px;
            padding: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: white;
            position: relative;
        }
        
        .test-card::before {
            content: attr(data-min-width);
            position: absolute;
            top: -10px;
            left: 10px;
            background: #3b82f6;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .test-card.has-min-width {
            border-color: #22c55e;
        }
        
        .test-card.no-min-width {
            border-color: #ef4444;
        }
        
        .card-content {
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f9fafb;
            border-radius: 4px;
            font-weight: 600;
            color: #374151;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-indicator.success {
            background: #22c55e;
        }
        
        .status-indicator.error {
            background: #ef4444;
        }
    </style>
</head>
<body>
    <div class="width-display" id="widthDisplay">
        Window: 0px
    </div>
    
    <div class="test-container">
        <h1>Dashboard Cards Min-Width Test</h1>
        <p>Testing that the last week sales card follows the same 1024px minimum width behavior as other dashboard cards</p>
        
        <div class="resize-instructions">
            <strong>🔍 Min-Width Test Instructions:</strong><br>
            1. Resize your browser window to make it narrower<br>
            2. All cards should stop shrinking at 1024px width<br>
            3. Green border = Has min-width constraint ✅<br>
            4. Red border = Missing min-width constraint ❌<br>
            5. Watch the window width display in the top-right corner
        </div>
        
        <div class="main-content">
            <!-- Test: Sales Cards Container -->
            <div class="test-section">
                <div class="test-title">Sales Cards Container</div>
                <div class="test-description">Should have 1024px minimum width</div>
                <div class="sales-cards-container test-card has-min-width" data-min-width="min-width: 1024px">
                    <div class="card-content">
                        <span class="status-indicator success"></span>
                        Sales Cards Container
                    </div>
                </div>
            </div>
            
            <!-- Test: Last Week Sales Card -->
            <div class="test-section">
                <div class="test-title">Last Week Sales Card (Fixed)</div>
                <div class="test-description">Should now have 1024px minimum width like other cards</div>
                <div class="last-week-sales-card test-card has-min-width" data-min-width="min-width: 1024px">
                    <div class="Sales-title-date-div">
                        <div class="title-date-section">
                            <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
                            <div class="title-date-text">
                                <span class="sales-card-title">Last Week's Sales</span>
                                <span class="sales-card-date">June 4, 2025 to June 10, 2025</span>
                            </div>
                        </div>
                        <div class="controls-section">
                            <div class="compare-div">
                                <div class="compare-btn">
                                    <img src="./assets/compare-ic.svg" alt="Compare" width="16" height="16" />
                                </div>
                            </div>
                            <div class="view-insights-btn">
                                <span>View Insights</span>
                            </div>
                        </div>
                    </div>
                    <div class="last-week-chart-container">
                        <div class="card-content">
                            <span class="status-indicator success"></span>
                            Last Week Sales Card - Now with min-width!
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Test: Listings Status Overview -->
            <div class="test-section">
                <div class="test-title">Listings Status Overview (Reference)</div>
                <div class="test-description">Reference card that already has 1024px minimum width</div>
                <div class="listings-status-overview test-card has-min-width" data-min-width="min-width: 1024px">
                    <div class="card-content">
                        <span class="status-indicator success"></span>
                        Listings Status Overview
                    </div>
                </div>
            </div>
            
            <!-- Test: Ad Spend (Reference) -->
            <div class="test-section">
                <div class="test-title">Ad Spend (Reference)</div>
                <div class="test-description">Reference card that already has 1024px minimum width</div>
                <div class="ad-spend test-card has-min-width" data-min-width="min-width: 1024px">
                    <div class="card-content">
                        <span class="status-indicator success"></span>
                        Ad Spend
                    </div>
                </div>
            </div>
            
            <!-- Test: Regular div without min-width -->
            <div class="test-section">
                <div class="test-title">Regular Div (No Min-Width)</div>
                <div class="test-description">This should shrink normally without min-width constraint</div>
                <div class="test-card no-min-width" data-min-width="no min-width">
                    <div class="card-content">
                        <span class="status-indicator error"></span>
                        Regular Div - Should shrink normally
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Update window width display
        function updateWidthDisplay() {
            const widthDisplay = document.getElementById('widthDisplay');
            const windowWidth = window.innerWidth;
            widthDisplay.textContent = `Window: ${windowWidth}px`;
            
            // Change color based on width
            if (windowWidth < 1024) {
                widthDisplay.style.background = '#dc2626';
                widthDisplay.style.color = 'white';
            } else {
                widthDisplay.style.background = '#1f2937';
                widthDisplay.style.color = 'white';
            }
        }
        
        // Update on resize
        window.addEventListener('resize', updateWidthDisplay);
        
        // Initial update
        updateWidthDisplay();
        
        // Check computed styles to verify min-width
        function checkMinWidths() {
            const cards = document.querySelectorAll('.test-card');
            cards.forEach(card => {
                const computedStyle = window.getComputedStyle(card);
                const minWidth = computedStyle.minWidth;
                
                console.log(`${card.className}: min-width = ${minWidth}`);
                
                // Update visual indicator based on actual computed style
                if (minWidth === '1024px') {
                    card.classList.remove('no-min-width');
                    card.classList.add('has-min-width');
                    card.style.borderColor = '#22c55e';
                } else if (card.classList.contains('no-min-width')) {
                    // Keep as no-min-width (expected)
                    card.style.borderColor = '#ef4444';
                } else {
                    // Should have min-width but doesn't
                    card.classList.remove('has-min-width');
                    card.classList.add('no-min-width');
                    card.style.borderColor = '#ef4444';
                }
            });
        }
        
        // Check min-widths after page load
        window.addEventListener('load', () => {
            setTimeout(checkMinWidths, 100);
        });
        
        console.log('Min-Width Test initialized');
        console.log('Resize the browser window to test min-width behavior');
    </script>
</body>
</html>
