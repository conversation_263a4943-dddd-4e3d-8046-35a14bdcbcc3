<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Last Week Sales Card Button Hover Test</title>
    <link rel="stylesheet" href="snapapp.css">
    <style>
        body {
            font-family: 'Amazon Ember', -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            color: #333;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 8px;
            color: #1a1a1a;
        }
        
        .test-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
        }
        
        .theme-controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }
        
        .theme-btn {
            padding: 8px 16px;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }
        
        .theme-btn:hover {
            background: #f9fafb;
        }
        
        .theme-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .comparison-item {
            padding: 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }
        
        .comparison-title {
            font-weight: 600;
            margin-bottom: 12px;
            color: #374151;
        }
        
        .button-demo {
            display: flex;
            gap: 16px;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .hover-instructions {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 20px;
            font-size: 13px;
            color: #92400e;
        }
        
        /* Reference buttons for comparison */
        .reference-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            background: #E9EBF2;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.2s ease;
            box-sizing: border-box;
        }
        
        .reference-btn:hover {
            background: #470CED;
        }
        
        [data-theme="dark"] .reference-btn {
            background: #292E38;
        }
        
        [data-theme="dark"] .reference-btn:hover {
            background: #470CED;
        }
        
        .color-demo {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-top: 12px;
            padding: 8px;
            background: white;
            border-radius: 4px;
            border: 1px solid #e5e7eb;
        }
        
        .color-swatch {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            border: 1px solid #e5e7eb;
        }
        
        .color-label {
            font-size: 12px;
            font-family: monospace;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Last Week Sales Card Button Hover Test</h1>
        <p>Testing the improved hover effects and dark mode background colors for the buttons</p>
        
        <div class="hover-instructions">
            <strong>🎯 Hover Test Instructions:</strong><br>
            1. Hover over the buttons to see the background color change<br>
            2. Switch between light and dark themes to test color consistency<br>
            3. Note: No scaling/lifting effects - only smooth background color transitions<br>
            4. Check that dark mode buttons match the listing-edit-ic background color
        </div>
        
        <div class="theme-controls">
            <button class="theme-btn active" id="lightTheme">Light Theme</button>
            <button class="theme-btn" id="darkTheme">Dark Theme</button>
        </div>
        
        <!-- Test: Last Week Sales Card Buttons -->
        <div class="test-section">
            <div class="test-title">Last Week Sales Card Buttons (Updated)</div>
            <div class="test-description">
                New hover effects with subtle animation and proper dark mode colors
            </div>
            
            <div class="last-week-sales-card">
                <div class="Sales-title-date-div">
                    <div class="title-date-section">
                        <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
                        <div class="title-date-text">
                            <span class="sales-card-title">Last Week's Sales</span>
                            <span class="sales-card-date">June 4, 2025 to June 10, 2025</span>
                        </div>
                    </div>
                    <div class="controls-section">
                        <div class="compare-div">
                            <div class="compare-btn" data-tooltip="Compare with Previous Periods">
                                <img src="./assets/compare-ic.svg" alt="Compare" width="16" height="16" />
                            </div>
                        </div>
                        <div class="view-insights-btn">
                            <span>View Insights</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Comparison with Reference Buttons -->
        <div class="test-section">
            <div class="test-title">Button Comparison</div>
            <div class="test-description">
                Comparing the new buttons with reference buttons from the listing system
            </div>
            
            <div class="comparison-grid">
                <div class="comparison-item">
                    <div class="comparison-title">Last Week Sales Buttons</div>
                    <div class="button-demo">
                        <div class="compare-btn">
                            <img src="./assets/compare-ic.svg" alt="Compare" width="16" height="16" />
                        </div>
                        <div class="view-insights-btn">
                            <span>View Insights</span>
                        </div>
                    </div>
                    <div class="color-demo">
                        <div class="color-swatch" style="background: #292E38;"></div>
                        <div class="color-label">Dark Mode: #292E38</div>
                    </div>
                </div>
                
                <div class="comparison-item">
                    <div class="comparison-title">Reference: Listing Edit Button</div>
                    <div class="button-demo">
                        <div class="reference-btn">
                            <img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" />
                        </div>
                    </div>
                    <div class="color-demo">
                        <div class="color-swatch" style="background: #292E38;"></div>
                        <div class="color-label">Dark Mode: #292E38</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sales Filter Div Comparison -->
        <div class="test-section">
            <div class="test-title">Sales Filter Background Comparison</div>
            <div class="test-description">
                Verifying that the button background matches the sales-filter-div background in dark mode
            </div>
            
            <div class="sales-filter-div">
                <div class="sales-filter-tab active">
                    <div class="tab-main">
                        <span class="tab-label">Time</span>
                    </div>
                </div>
                <div class="sales-filter-tab">
                    <div class="tab-main">
                        <span class="tab-label">Units</span>
                    </div>
                </div>
                <div class="sales-filter-tab">
                    <div class="tab-main">
                        <span class="tab-label">Royalties</span>
                    </div>
                </div>
            </div>
            
            <div style="margin-top: 16px;">
                <div class="color-demo">
                    <div class="color-swatch" style="background: #292E38;"></div>
                    <div class="color-label">Sales Filter Dark Mode: #292E38</div>
                </div>
            </div>
        </div>
        
        <!-- Hover Effects Demo -->
        <div class="test-section">
            <div class="test-title">Hover Effects Demo</div>
            <div class="test-description">
                Demonstrating the simplified hover effects: smooth background color change only
            </div>
            
            <div style="display: flex; gap: 20px; align-items: center; padding: 20px;">
                <div class="compare-btn">
                    <img src="./assets/compare-ic.svg" alt="Compare" width="16" height="16" />
                </div>
                <div class="view-insights-btn">
                    <span>View Insights</span>
                </div>
                <div style="font-size: 14px; color: #6b7280;">
                    ← Hover over these buttons to see the effects
                </div>
            </div>
        </div>
    </div>

    <script>
        // Theme controls
        document.getElementById('lightTheme').addEventListener('click', function() {
            document.documentElement.setAttribute('data-theme', 'light');
            document.querySelectorAll('.theme-btn').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
        });
        
        document.getElementById('darkTheme').addEventListener('click', function() {
            document.documentElement.setAttribute('data-theme', 'dark');
            document.querySelectorAll('.theme-btn').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
        });
        
        // Log computed styles for verification
        function logButtonStyles() {
            const compareBtn = document.querySelector('.compare-btn');
            const viewInsightsBtn = document.querySelector('.view-insights-btn');
            const salesFilter = document.querySelector('.sales-filter-div');
            
            if (compareBtn && viewInsightsBtn && salesFilter) {
                const theme = document.documentElement.getAttribute('data-theme') || 'light';
                console.log(`\n=== ${theme.toUpperCase()} THEME STYLES ===`);
                console.log('Compare Button BG:', window.getComputedStyle(compareBtn).backgroundColor);
                console.log('View Insights Button BG:', window.getComputedStyle(viewInsightsBtn).backgroundColor);
                console.log('Sales Filter BG:', window.getComputedStyle(salesFilter).backgroundColor);
            }
        }
        
        // Log styles on theme change
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(logButtonStyles, 100);
        });
        
        document.getElementById('lightTheme').addEventListener('click', () => {
            setTimeout(logButtonStyles, 100);
        });
        
        document.getElementById('darkTheme').addEventListener('click', () => {
            setTimeout(logButtonStyles, 100);
        });
        
        console.log('Button Hover Test initialized');
        console.log('Expected: Smooth background color transitions and consistent dark mode colors');
    </script>
</body>
</html>
