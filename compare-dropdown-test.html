<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Compare Dropdown Test</title>
    <link rel="stylesheet" href="snapapp.css">
    <script src="components/charts/snap-charts.js"></script>
    <style>
        body {
            margin: 0;
            padding: 40px;
            background: #f5f5f5;
            font-family: 'Amazon Ember', sans-serif;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #1f2937;
        }
        
        .test-description {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 40px;
            line-height: 1.5;
        }
        
        .demo-section {
            border: 2px dashed #e5e7eb;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            background: #f9fafb;
        }
        
        .demo-card {
            display: inline-block;
            background: white;
            border: 1.5px solid #e5e7eb;
            border-radius: 14px;
            padding: 24px;
            min-width: 400px;
        }
        
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 8px 16px;
            background: #470CED;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
        }
        
        .instructions {
            margin-top: 30px;
            padding: 20px;
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .instructions h4 {
            margin: 0 0 10px 0;
            color: #0369a1;
        }
        
        .instructions ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()">Toggle Dark Mode</button>
    
    <div class="test-container">
        <div class="test-title">Compare Dropdown Test</div>
        <div class="test-description">
            Testing the compare dropdown functionality for the Last Week Sales card. 
            The dropdown should appear centered below the compare button with proper hover behavior.
        </div>
        
        <div class="demo-section">
            <div class="demo-card last-week-sales-card">
                <div class="Sales-title-date-div">
                    <div class="title-date-section">
                        <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
                        <div class="title-date-text">
                            <span class="sales-card-title">Last Week's Sales</span>
                            <span class="sales-card-date">June 4, 2025 to June 10, 2025</span>
                        </div>
                    </div>
                    <div class="controls-section">
                        <div class="compare-div">
                            <div class="compare-btn" data-tooltip="Compare with Previous Periods">
                                <img src="./assets/compare-ic.svg" alt="Compare" width="16" height="16" />
                            </div>
                            <!-- Compare Dropdown Menu -->
                            <div class="compare-dropdown" id="compare-dropdown">
                                <div class="compare-dropdown-item" data-value="none">
                                    <div class="compare-checkbox">
                                        <img src="./assets/checkbox-ic.svg" alt="Checkbox" width="20" height="20" />
                                    </div>
                                    <span class="compare-dropdown-text">Don't compare</span>
                                </div>
                                <div class="compare-dropdown-item" data-value="week">
                                    <div class="compare-checkbox">
                                        <img src="./assets/checkbox-ic.svg" alt="Checkbox" width="20" height="20" />
                                    </div>
                                    <span class="compare-dropdown-text">Compare with previous week</span>
                                </div>
                                <div class="compare-dropdown-item" data-value="month">
                                    <div class="compare-checkbox">
                                        <img src="./assets/checkbox-ic.svg" alt="Checkbox" width="20" height="20" />
                                    </div>
                                    <span class="compare-dropdown-text">Compare with previous month</span>
                                </div>
                                <div class="compare-dropdown-item" data-value="year">
                                    <div class="compare-checkbox">
                                        <img src="./assets/checkbox-ic.svg" alt="Checkbox" width="20" height="20" />
                                    </div>
                                    <span class="compare-dropdown-text">Compare with previous year</span>
                                </div>
                            </div>
                        </div>
                        <div class="view-insights-btn">
                            <span>View Insights</span>
                        </div>
                    </div>
                </div>

                <!-- Chart Container -->
                <div class="last-week-chart-container" id="last-week-chart-container" style="margin-top: 24px; height: 300px; width: 100%;"></div>
            </div>
        </div>
        
        <div class="instructions">
            <h4>Test Instructions (Updated with Realistic Date Calculations):</h4>
            <ul>
                <li><strong>Click</strong> the compare button to open/close the dropdown</li>
                <li><strong>Select</strong> any option to close the dropdown and see the button change to purple with white icon</li>
                <li><strong>Click outside</strong> the dropdown area to close it without selecting</li>
                <li><strong>Select "Don't compare"</strong> to return to normal button styling</li>
                <li><strong>Toggle dark mode</strong> to test theme compatibility</li>
                <li><strong>Date Calculations:</strong> Now uses Pacific Time and calculates proper comparison periods:
                    <ul>
                        <li><strong>Previous Week:</strong> Exact same 7-day period from previous week</li>
                        <li><strong>Previous Month:</strong> Same week from previous month</li>
                        <li><strong>Previous Year:</strong> Same week from previous year</li>
                    </ul>
                </li>
                <li><strong>Independent Marketplace Activity:</strong> Comparison periods have different marketplace sales patterns:
                    <ul>
                        <li><strong>Previous Week:</strong> 3-6 active marketplaces (similar to current)</li>
                        <li><strong>Previous Month:</strong> 2-7 active marketplaces (moderate variation)</li>
                        <li><strong>Previous Year:</strong> 1-7 active marketplaces (significant variation)</li>
                    </ul>
                </li>
                <li><strong>Realistic Data:</strong> Comparison data varies appropriately (week: ±20%, month: ±40%, year: ±60%)</li>
                <li><strong>Note:</strong> Dropdown uses 20px checkboxes, auto-width, 20px gap, and marketplace dropdown colors</li>
            </ul>
        </div>
    </div>
    
    <script>
        // Theme toggle functionality
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            
            const button = document.querySelector('.theme-toggle');
            button.textContent = newTheme === 'dark' ? 'Switch to Light Mode' : 'Toggle Dark Mode';
        }
        
        // Initialize compare dropdown functionality
        function initializeCompareDropdown() {
            console.log('📊 Initializing compare dropdown...');
            
            const compareBtn = document.querySelector('.compare-btn');
            const compareDropdown = document.querySelector('.compare-dropdown');
            const dropdownItems = document.querySelectorAll('.compare-dropdown-item');
            
            if (!compareBtn || !compareDropdown || dropdownItems.length === 0) {
                console.error('❌ Compare dropdown elements not found');
                return;
            }
            
            let currentSelection = 'none'; // Default to "Don't compare"
            let dropdownVisible = false;
            
            // Set default selection
            updateSelection('none');
            
            // Compare button click handler
            compareBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                console.log('📊 Compare button clicked');
                
                if (dropdownVisible) {
                    hideDropdown();
                } else {
                    showDropdown();
                }
            });
            
            // Dropdown item click handlers
            dropdownItems.forEach(item => {
                item.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const value = item.getAttribute('data-value');
                    console.log('📊 Compare option selected:', value);
                    
                    updateSelection(value);
                    hideDropdown();
                });
            });
            
            // Close dropdown when clicking outside - matches marketplace dropdown behavior
            function handleClickOutside(e) {
                if (!compareBtn.contains(e.target) && !compareDropdown.contains(e.target)) {
                    hideDropdown();
                }
            }
            document.addEventListener('click', handleClickOutside);
            
            function showDropdown() {
                compareDropdown.classList.add('show');
                dropdownVisible = true;
                console.log('📊 Compare dropdown shown');
            }
            
            function hideDropdown() {
                compareDropdown.classList.remove('show');
                dropdownVisible = false;
                console.log('📊 Compare dropdown hidden');
            }
            
            function updateSelection(value) {
                currentSelection = value;
                
                // Update visual selection in dropdown
                dropdownItems.forEach(item => {
                    if (item.getAttribute('data-value') === value) {
                        item.classList.add('selected');
                    } else {
                        item.classList.remove('selected');
                    }
                });
                
                // Update compare button styling
                if (value === 'none') {
                    compareBtn.classList.remove('active');
                } else {
                    compareBtn.classList.add('active');
                }

                // Update chart compare mode
                updateChartCompareMode(value);

                console.log('📊 Compare selection updated:', value);
            }

            function updateChartCompareMode(compareValue) {
                console.log('📊 Updating chart compare mode:', compareValue);

                // Find the chart container and get the chart instance
                const chartContainer = document.querySelector('#last-week-chart-container');
                if (!chartContainer || !chartContainer.snapChart) {
                    console.warn('📊 Chart instance not found');
                    return;
                }

                const chart = chartContainer.snapChart;

                if (compareValue === 'none') {
                    // Disable compare mode
                    chart.options.compareMode = false;
                    chart.options.compareData = null;
                    console.log('📊 Compare mode disabled');
                } else {
                    // Enable compare mode with appropriate comparison data
                    chart.options.compareMode = true;

                    // Generate comparison data based on selected period
                    const comparisonData = generateComparisonData(compareValue, chart.data);
                    chart.options.compareData = comparisonData;

                    console.log('📊 Compare mode enabled with:', compareValue);
                }

                // Re-render the chart with new compare settings
                chart.render();
            }

            /**
             * Calculate comparison period dates based on current period and comparison type
             * Uses Pacific Time timezone for consistency with dashboard dates
             */
            function calculateComparisonPeriod(compareValue) {
                // Get current time in Pacific Time (matching dashboard date logic)
                const now = new Date();
                const pacificTime = new Date(now.toLocaleString("en-US", {timeZone: "America/Los_Angeles"}));

                const today = new Date(pacificTime);
                const currentEndDate = new Date(today);
                currentEndDate.setDate(today.getDate() - 1); // Yesterday (current period end)
                const currentStartDate = new Date(currentEndDate);
                currentStartDate.setDate(currentEndDate.getDate() - 6); // 7 days ago (current period start)

                let comparisonStartDate, comparisonEndDate;

                switch (compareValue) {
                    case 'week':
                        // Previous week: exact same 7-day period from previous week
                        comparisonEndDate = new Date(currentStartDate);
                        comparisonEndDate.setDate(currentStartDate.getDate() - 1); // Day before current period start
                        comparisonStartDate = new Date(comparisonEndDate);
                        comparisonStartDate.setDate(comparisonEndDate.getDate() - 6); // 7 days before that
                        break;

                    case 'month':
                        // Previous month: same week from previous month
                        comparisonStartDate = new Date(currentStartDate);
                        comparisonStartDate.setMonth(currentStartDate.getMonth() - 1);
                        comparisonEndDate = new Date(currentEndDate);
                        comparisonEndDate.setMonth(currentEndDate.getMonth() - 1);
                        break;

                    case 'year':
                        // Previous year: same week from previous year
                        comparisonStartDate = new Date(currentStartDate);
                        comparisonStartDate.setFullYear(currentStartDate.getFullYear() - 1);
                        comparisonEndDate = new Date(currentEndDate);
                        comparisonEndDate.setFullYear(currentEndDate.getFullYear() - 1);
                        break;

                    default:
                        console.error('❌ Invalid comparison value:', compareValue);
                        return null;
                }

                return {
                    startDate: comparisonStartDate,
                    endDate: comparisonEndDate,
                    currentStartDate: currentStartDate,
                    currentEndDate: currentEndDate
                };
            }

            function generateComparisonData(compareValue, currentData) {
                if (!currentData || currentData.length === 0) {
                    return null;
                }

                // Calculate the comparison period dates based on the current period
                const comparisonPeriod = calculateComparisonPeriod(compareValue);
                if (!comparisonPeriod) {
                    console.error('❌ Failed to calculate comparison period');
                    return null;
                }

                console.log(`📊 Comparison period: ${comparisonPeriod.startDate.toDateString()} to ${comparisonPeriod.endDate.toDateString()}`);

                // Generate realistic comparison data with proper dates
                return generateRealisticComparisonData(currentData, comparisonPeriod, compareValue);
            }

            /**
             * Generate realistic comparison data with independent marketplace activity patterns
             */
            function generateRealisticComparisonData(currentData, comparisonPeriod, compareValue) {
                const comparisonData = [];

                for (let i = 0; i < currentData.length; i++) {
                    const currentDayData = currentData[i];

                    // Calculate the comparison date for this day
                    const comparisonDate = new Date(comparisonPeriod.startDate);
                    comparisonDate.setDate(comparisonPeriod.startDate.getDate() + i);

                    // Generate independent marketplace activity pattern for comparison period
                    const comparisonMarketplaces = generateIndependentMarketplaceActivity(currentDayData.marketplaces, compareValue);

                    // Create comparison day data with proper date formatting
                    const comparisonDayData = {
                        month: comparisonDate.toLocaleDateString('en-US', { month: 'short', timeZone: 'America/Los_Angeles' }).toUpperCase(),
                        day: comparisonDate.getDate().toString().padStart(2, '0'),
                        year: comparisonDate.getFullYear().toString().slice(-2),
                        marketplaces: comparisonMarketplaces,
                        sales: comparisonMarketplaces.reduce((sum, mp) => sum + mp.sales, 0),
                        royalties: comparisonMarketplaces.reduce((sum, mp) => sum + mp.royalties, 0),
                        returns: comparisonMarketplaces.reduce((sum, mp) => sum + mp.returns, 0),
                        values: comparisonMarketplaces.map(mp => mp.sales),
                        labels: comparisonMarketplaces.map(mp => mp.code)
                    };

                    comparisonData.push(comparisonDayData);
                }

                return comparisonData;
            }

            /**
             * Generate independent marketplace activity pattern for comparison period
             * Creates realistic variation in which marketplaces have sales activity
             */
            function generateIndependentMarketplaceActivity(currentMarketplaces, compareValue) {
                // All available marketplaces (maintain consistent structure)
                const allMarketplaceCodes = ["US", "UK", "DE", "FR", "IT", "ES", "JP"];

                // Determine how many marketplaces should have sales activity in comparison period
                const numActiveMarketplaces = getRandomActiveMarketplaceCount(compareValue);

                // Randomly select which marketplaces are active (independent of current period)
                const shuffledMarketplaces = [...allMarketplaceCodes].sort(() => 0.5 - Math.random());
                const activeMarketplaceCodes = shuffledMarketplaces.slice(0, numActiveMarketplaces);

                console.log(`📊 Comparison period active marketplaces: ${activeMarketplaceCodes.join(', ')} (${numActiveMarketplaces} total)`);

                // Generate comparison marketplace data with independent activity pattern
                return currentMarketplaces.map(currentMp => {
                    const isActiveInComparison = activeMarketplaceCodes.includes(currentMp.code);

                    if (isActiveInComparison) {
                        // Generate realistic sales data for active marketplace
                        const variationMultiplier = getRealisticVariationMultiplier(compareValue);

                        // Base sales amount - could be higher or lower than current period
                        const baseSales = Math.floor(Math.random() * 90) + 10; // 10-100 sales range
                        const comparisonSales = Math.max(0, Math.round(baseSales * variationMultiplier));

                        // Calculate royalties based on sales (realistic relationship)
                        const royaltyRate = 0.1 + (Math.random() * 0.15); // 10-25% royalty rate
                        const comparisonRoyalties = Math.round(comparisonSales * royaltyRate);

                        // Returns should be proportional to sales but with some randomness
                        let comparisonReturns = 0;
                        if (comparisonSales > 0) {
                            const returnRate = Math.random() < 0.7 ? 0 : Math.random() * 0.1; // 0-10% return rate
                            comparisonReturns = Math.round(comparisonSales * returnRate);
                        }

                        return {
                            ...currentMp,
                            sales: comparisonSales,
                            royalties: comparisonRoyalties,
                            returns: comparisonReturns
                        };
                    } else {
                        // Marketplace has no sales activity in comparison period
                        return {
                            ...currentMp,
                            sales: 0,
                            royalties: 0,
                            returns: 0
                        };
                    }
                });
            }

            /**
             * Get random number of active marketplaces based on comparison type
             * Different comparison periods may have different marketplace activity levels
             */
            function getRandomActiveMarketplaceCount(compareValue) {
                switch (compareValue) {
                    case 'week':
                        // Previous week: similar activity level (3-6 marketplaces)
                        return Math.floor(Math.random() * 4) + 3;
                    case 'month':
                        // Previous month: moderate variation (2-7 marketplaces)
                        return Math.floor(Math.random() * 6) + 2;
                    case 'year':
                        // Previous year: significant variation (1-7 marketplaces)
                        return Math.floor(Math.random() * 7) + 1;
                    default:
                        return 4; // Default to 4 marketplaces
                }
            }

            /**
             * Get realistic variation multiplier based on comparison type
             */
            function getRealisticVariationMultiplier(compareValue) {
                switch (compareValue) {
                    case 'week':
                        // Previous week: 80-120% of base values (small weekly variation)
                        return 0.8 + (Math.random() * 0.4);
                    case 'month':
                        // Previous month: 60-140% of base values (moderate monthly variation)
                        return 0.6 + (Math.random() * 0.8);
                    case 'year':
                        // Previous year: 40-160% of base values (larger year-over-year variation)
                        return 0.4 + (Math.random() * 1.2);
                    default:
                        return 1.0;
                }
            }

            console.log('✅ Compare dropdown initialized');
        }

        // Initialize chart with sample data
        function initializeTestChart() {
            const chartContainer = document.querySelector('#last-week-chart-container');
            if (!chartContainer) {
                console.error('Chart container not found');
                return;
            }

            // Generate sample data for testing
            const sampleData = generateSampleData();

            const chart = new SnapChart({
                container: chartContainer,
                type: 'stacked-column',
                data: sampleData,
                options: {
                    responsive: true,
                    animate: true,
                    height: 300,
                    compareMode: false // Start with compare mode disabled
                }
            });

            // Store chart instance on container for dropdown access
            chartContainer.snapChart = chart;

            console.log('✅ Test chart initialized');
        }

        function generateSampleData() {
            const data = [];
            const marketplaces = [
                { code: 'US', name: 'United States' },
                { code: 'UK', name: 'United Kingdom' },
                { code: 'DE', name: 'Germany' },
                { code: 'FR', name: 'France' }
            ];

            // Generate 7 days of data
            for (let i = 0; i < 7; i++) {
                const date = new Date();
                date.setDate(date.getDate() - (6 - i));

                const dayMarketplaces = marketplaces.map(mp => ({
                    ...mp,
                    sales: Math.floor(Math.random() * 500) + 100,
                    royalties: Math.floor(Math.random() * 50) + 10,
                    returns: Math.floor(Math.random() * 20) + 1
                }));

                const dayData = {
                    month: date.toLocaleDateString('en-US', { month: 'short' }).toUpperCase(),
                    day: date.getDate().toString().padStart(2, '0'),
                    year: date.getFullYear().toString().slice(-2),
                    marketplaces: dayMarketplaces,
                    sales: dayMarketplaces.reduce((sum, mp) => sum + mp.sales, 0),
                    royalties: dayMarketplaces.reduce((sum, mp) => sum + mp.royalties, 0),
                    returns: dayMarketplaces.reduce((sum, mp) => sum + mp.returns, 0)
                };

                data.push(dayData);
            }

            return data;
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            initializeTestChart();
            initializeCompareDropdown();
        });
    </script>
</body>
</html>
