# Current Task: Implement Unchecked Checkboxes in Compare Button Menu Items

## Task Description
Update the compare button dropdown menu items to use unchecked checkboxes that match the style and behavior of the snap-image-studio.js checkboxes.

## Implementation Steps
- [x] Task 1: Update HTML structure to use `uncheckedbox-ic.svg` for all items initially (except first item which should be checked by default)
- [x] Task 2: Update CSS to remove opacity-based visibility system and make checkboxes always visible
- [x] Task 3: Update JavaScript `updateSelection` function to change image sources instead of using opacity classes
- [x] Task 4: Ensure first item (none) is selected by default with correct checkbox image

## Changes Made

### 1. HTML Structure Update (dashboard.js)
- Changed all compare dropdown items to use `uncheckedbox-ic.svg` initially
- First item (none) remains with `checkbox-ic.svg` as it's selected by default

### 2. CSS Update (snapapp.css)
- Removed `opacity: 0` and `opacity: 1` rules for checkbox visibility
- Removed `.selected` class-based opacity control
- Made checkboxes always visible with smooth transitions

### 3. JavaScript Logic Update (dashboard.js)
- Updated `updateSelection` function to change image sources directly
- Selected items now use `checkbox-ic.svg`
- Non-selected items use `uncheckedbox-ic.svg`
- Maintains the same pattern as snap-image-studio.js checkboxes

## Result
The compare dropdown checkboxes now behave exactly like the snap-image-studio checkboxes:
- All checkboxes are always visible
- Selected items show checked checkbox icon
- Non-selected items show unchecked checkbox icon
- Smooth transitions between states
- Consistent with the existing design system
